/* source-sans-pro-300 - latin_latin-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 300;
  //src: url("src/assets/layout/fonts/SourceSansPro-Light.eot"); /* IE9 Compat Modes */
  src: local('Source Sans Pro Light'), local('SourceSansPro-Light'),
    //url("../fonts/SourceSansPro-Light.eot']}?#iefix") format('embedded-opentype'), /* IE6-IE8 */
  url("../../layout/fonts/SourceSansPro-Light.woff2") format('woff2'), /* Super Modern Browsers */
  url("../../layout/fonts/SourceSansPro-Light.woff") format('woff'), /* Modern Browsers */
  url("../../layout/fonts/SourceSansPro-Light.ttf") format('truetype') /* Safari, Android, iOS */
  //url("../fonts/SourceSansPro-Light.svg']}#SourceSansPro-Light") format('svg'); /* Legacy iOS */
}

/* source-sans-pro-regular - latin_latin-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  //src: url("../fonts/SourceSansPro-Regular.eot']}"); /* IE9 Compat Modes */
  src: local('Source Sans Pro'), local('SourceSansPro-Regular'),
    //url("../fonts/SourceSansPro-Regular.eot']}?#iefix") format('embedded-opentype'), /* IE6-IE8 */
  url("../../layout/fonts/SourceSansPro-Regular.woff2") format('woff2'), /* Super Modern Browsers */
  url("../../layout/fonts/SourceSansPro-Regular.woff") format('woff'), /* Modern Browsers */
  url("../../layout/fonts/SourceSansPro-Regular.ttf") format('truetype') /* Safari, Android, iOS */
  //url("../fonts/SourceSansPro-Regular.svg']}#SourceSansPro-Regular") format('svg'); /* Legacy iOS */
}

/* source-sans-pro-700 - latin_latin-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 700;
  //src: url("../fonts/SourceSansPro-Bold.eot']}"); /* IE9 Compat Modes */
  src: local('Source Sans Pro Bold'), local('SourceSansPro-Bold'),
    //url("../fonts/SourceSansPro-Bold.eot']}?#iefix") format('embedded-opentype'), /* IE6-IE8 */
  url("../../layout/fonts/SourceSansPro-Bold.woff2") format('woff2'), /* Super Modern Browsers */
  url("../../layout/fonts/SourceSansPro-Bold.woff") format('woff'), /* Modern Browsers */
  url("../../layout/fonts/SourceSansPro-Bold.ttf") format('truetype') /* Safari, Android, iOS */
  //url("../fonts/SourceSansPro-Bold.svg']}#SourceSansPro-Bold") format('svg'); /* Legacy iOS */
}

@font-face {
  font-family: 'Nunito';
  src: url("../../layout/fonts/Nunito-VariableFont_wght.ttf");
}
