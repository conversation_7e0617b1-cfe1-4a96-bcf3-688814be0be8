.exception-body {
    background-color: #f8f8f8;
    box-sizing: border-box;
    
    .exception-panel {
        background-color: #f7f7f7;
        width: 800px;
        margin: 0 auto;
        text-align: center;
        padding: 100px;
        box-sizing: border-box;
        
        img {
            width: 512px;
        }

        .line {
            width: 100%;
            border: 1px solid #979797;
            margin-top: 40px;
        }
        
        h1 {
            font-size: 22px;
            color: #2d353c;
            margin-top: 50px;
            margin-bottom: 10px;
        }
        
        p {
            font-size: 15px;
            margin: 0 0 30px 0px;
            color: #6a6a7d;
        }
    }
}

@media (max-width: 1024px) {
    .exception-body {
        .exception-panel {
            width: 100%;
            padding: 80px 50px 50px 50px;

            img {
                width: 384px;
            }
        }
    }
}

@media (max-width: 640px) {
    .exception-body {
          
        .exception-panel {
            width: 100%;
            padding: 80px 50px 50px 50px;

            img {
                width: 256px;
            }
        }
    }
}