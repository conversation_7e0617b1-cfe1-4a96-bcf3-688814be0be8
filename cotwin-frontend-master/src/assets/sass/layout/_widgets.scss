.widget-overview-box {
    padding: 15px 10px;
    text-align: left;
    overflow: hidden;
    margin-bottom: 0px !important;
    background-color: #ffffff;
    @include border-radius(3px);
    color: $textColor;

    .overview-box-icon {
        text-align: center;
        position: relative;

        img {
            width: 80px;
            height: 60px;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            margin: auto;
        }
    }

    .overview-box-name {
        font-size: 16px;
        display: inline-block;
        width: 100%;
    }

    .overview-box-count {
        font-size: 36px;
        font-weight: bold;
    }

    .overview-box-rate {
        @include border-radius(3px);
        padding: 2px 4px;
        background-color: $primaryColor;
        font-weight: bold;
        color: $highlightTextColor;
        display: inline-block;
        margin-top: 4px;
    }

    &.overview-box-1 {
        .overview-box-footer {
            background-color: #3192e1;
        }
    }

    &.overview-box-2 {
        .overview-box-icon {
            img {
                height: 50px;
            }
        }

        .overview-box-footer {
            background-color: #e42a7b;
        }
    }

    &.overview-box-3 {
        .overview-box-footer {
            background-color: #dfb051;
        }

        .overview-box-rate {
            background-color: #f3745a;
            color: #ffffff;
        }
    }

    &.overview-box-4 {
        .overview-box-icon {
            img {
                height: 70px;
            }
        }

        .overview-box-footer {
            background-color: #d97c3e;
        }
    }

    &.control-panel {
        background-color: #ffffff;
    }
}

.widget-map {
    &.card {
        padding: 0;

        h4 {
            padding: .5em;
            margin-bottom: 0;
        }
    }

    .map{
        width: 100%;
        height: 400px;
        background-image: url("../images/sample-map2x.png");
        background-size: cover;
    }
}

.widget-weather-box {
    height: 100%;

    &.card {
        padding: 0;
    }

    .left {
        background-image: url("../images/green-blue2x.png");
        background-size: 100% 100%;

        > div {
            height: 50%;
            text-align: center;
            color: #ffffff;

            .large {
                padding-top: 30%;
                font-size: 36px;
                font-weight: bold;
            }

            .normal {
                font-size: 14px;
            }

            &.stripe {
                height: 0px;
                padding: 0px;
                border-top: 1px solid #ABF1B5;
            }

            @media (max-width: 640px) {
                &{
                    width: 49%;
                    height: 100%;

                    .large {
                        padding-top: 10%;
                    }
                }

                &.stripe {
                    height: 100%;
                    width:0px;
                    border-top: 0px;
                    border-left: 1px solid #ABF1B5;
                }
            }
        }
    }

    .wrapper {
        padding: 15px;
        color: $textColor;

        div.large {
            font-size: 20px;
            font-weight: bold;
        }
    }
}

.widget-control-panel {
    padding: 0;

    .left-controls {
        padding: 0px;

        span {
            float: left;
            padding: 16px;
        }
    }
    .right-controls {
        padding: 0px;

        .p-col-4 {
            padding: 0px;
            float: right;
        }

        a {
            border-left: solid 0.5px #e6e6e6;
            display: inline-block;
            height: 100%;
            width: 100%;
            box-sizing: border-box;
            line-height: 48px;
            color: #ababab;
            cursor: pointer;
            text-align: center;

            i {
                font-size: 24px;
                line-height: inherit;
            }

            &:hover {
                color: $highlightTextColor;
                background-color: $primaryColor;
            }
        }
    }
}

.widget-user-card {
    text-align: center;
    border:1px solid $contentBorderColor;
    @include border-radius($borderRadius);

    .user-card-header {
        height: 100px;
        background-color: #f1f1f1;
    }

    .user-card-content {
        margin-top: -25px;
        height: 300px;
        background-color: #ffffff;
        border-top: 1px solid $dividerColor;
        padding: 5px 15px;

        img {
            margin-top: -55px;
        }

        span {
            display: block;

            &.user-card-name {
                font-size: 16px;
                font-weight: 700;
                margin-bottom: 12px;
            }

            &.user-card-role {
                margin-bottom: 25px;
            }
        }
    }

    .user-card-footer {
        padding: 5px;
        height: 50px;
        border-top: 1px solid $dividerColor;
        background-color: #f9f9f9;

        span {
            display: block;
            &:first-child {
                font-weight: 700;
            }
        }
    }
}

.widget-chat {
    &.card {
        padding: 0;

        h4 {
            padding: .5em;
            margin-bottom: 0;
        }
    }

    ul {
        padding: 12px;
        margin: 0;
        list-style-type: none;

        li {
            padding: 6px 0;
            img {
                width: 36px;
                float: left;
            }

            span {
                padding: 6px 12px;
                float: left;
                display: inline-block;
                margin: 4px 0;
                @include border-radius(3px);
            }

            &.message-from {
                img, span {
                    float: left;
                }

                img {
                    margin-right: 8px;
                }

                span {
                    background-color: #e4fed9;
                }
            }

            &.message-own {
                img, span {
                    float: right;
                }

                img {
                    margin-left: 8px;
                }

                span {
                    background: #fff7e1;
                }
            }
        }
    }

    .new-message {
        height: 40px;
        border-top: 1px solid $dividerColor;
        color: #afafc0;

        .message-attachment {
            display: inline-block;
            border-right: 1px solid $dividerColor;
            width: 40px;
            line-height: 40px;
            height: 100%;
            text-align: center;

            i {
                line-height: inherit;
                font-size: 24px;
            }
        }

        .message-input {
            position: relative;
            top: -4px;
            width: calc(100% - 100px);
            display: inline-block;
            padding-left: 10px;

            input {
                border: 0 none;
                font-size: 13px;
                width: 100%;
                background-color: transparent;
                outline: 0 none;
                color: $textSecondaryColor;
            }
        }
    }
}

.widget-morpheus-overview {
    img {
        width: 100%;
    }

    .article-date {
        font-weight: bold;
        color: #afafc0;
        display: inline-block;
        margin-top: 6px;
    }

    h3 {
        margin: 12px 0;
        font-weight: bold;
        color: #2d353c;
    }

    p {
        margin: 0 0 20px 0;
        color: #525262;
    }
}

.widget-activity-feed {
    text-align: center;

    h3 {
        color: #525262;
        margin: 20px 0 5px 0;
        font-weight: bold;
        font-size: 13px;
    }

    p {
        color: $textSecondaryColor;
        margin: 0;
        font-size: 13px;
    }

    .p-col-12 {
        padding: 20px;

        span {
            display: block;
            font-weight: bold;
            color: #6a6a7d;
        }
    }

    .knob {
        width: 120px;
        height: 120px;
        line-height: 100px;
        margin-top: 20px;
        font-size: 30px;
        color: #6a6a7d;
        @include border-radius(50%);
        display: inline-block;

        &.income {
            border: 10px solid $green;
            border-left-color: lighten($green,30%);
        }

        &.tax {
            border: 10px solid $yellow;
            border-left-color: lighten($yellow,30%);
        }

        &.invoice {
            padding: initial;
            border: 10px solid $black;
            border-left-color: lighten($black,30%);
        }

        &.expense {
            border: 10px solid $blue;
            border-left-color: lighten($blue,20%);
        }
    }
}

.widget-timeline {
    height: 100%;
    box-sizing: border-box;

    > .p-grid {
        .p-col-3 {
            font-size: 14px;
            position: relative;
            border-right: 1px solid $dividerColor;

            i {
                background-color: transparent;
                font-size: 24px;
                position: absolute;
                top: 6px;
                right: -12px;
            }
        }

        .p-col-9 {
            padding-left: 1.5em;
            .event-owner {
                font-weight: bold;
            }

            .event-text {
                color: $textSecondaryColor;
                font-size: 14px;
                display: block;
                padding-bottom: 20px;
            }

            .event-content {
                img {
                    width: 100%;
                }
            }
        }
    }
}

.widget-activity {
    .activity-list {
        list-style-type: none;
        padding: 0;
        margin: 0;

        li {
            border-bottom: 1px solid $dividerColor;
            padding: 15px 0 9px 9px;

            .count {
                font-size: 24px;
                color: #ffffff;
                background-color: $blue;
                font-weight: bold;
                width: 75px;
                padding: 5px;
                @include border-radius(2px);
            }

            &:first-child {
                border-top: 1px solid $dividerColor;
            }

            &:last-child {
                border: 0;
            }

            .p-grid {
                padding-top: .5em;
            }

            .p-col-6:first-child {
                font-size: 18px;
            }

            .p-col-6:last-child {
                text-align: right;
                color: $textSecondaryColor;
            }
        }
    }
}

.widget-contact-form {
    overflow: hidden;

    .p-panel {
        min-height: 340px;
    }

    .p-col-12 {
        padding: 6px 12px;
    }

    .p-button {
        margin-top: 4px;
    }
}

.widget-contacts {
    overflow: hidden;

    > .p-panel {
        min-height: 340px;
    }

    .p-panel-content{
        padding: 0px !important;
    }

    ul {
        list-style-type: none;
        padding: 0;
        margin: 0;

        li {
            border-bottom: 1px solid $dividerColor;
            padding: 9px;
            width: 100%;
            box-sizing: border-box;
            text-decoration: none;
            position: relative;
            display: block;
            @include border-radius(2px);
            @include transition(background-color .2s);

            img {
                float: left;
                margin-right: 8px;
            }

            .contact-info {
                float: left;

                .name {
                    display: block;
                    margin-top: 4px;
                    font-size: 14px;
                }

                .location {
                    margin-top: 4px;
                    display: block;
                    font-size: 12px;
                    color: $textSecondaryColor;
                }
            }

            .contact-actions {
                float: right;
                padding-top:12px;

                .connection-status {
                    color: $highlightTextColor;
                    padding: 2px 3px;
                    @include border-radius($borderRadius);

                    &.online {
                        background-color: $primaryColor;
                    }

                    &.offline {
                        background-color: #f3745a;
                        color: #ffffff;
                    }
                }

                i {
                    color: $textSecondaryColor;
                    margin-left: 5px;
                }
            }

            &:last-child {
                border: 0;
            }
        }
    }
}
