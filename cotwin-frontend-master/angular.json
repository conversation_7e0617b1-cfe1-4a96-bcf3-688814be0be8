{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"morpheus": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/morpheus", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "aot": true, "assets": ["src/assets", "src/favicon.ico", "src/upload.php"], "styles": ["src/styles.scss", "node_modules/quill/dist/quill.core.css", "node_modules/quill/dist/quill.snow.css", "node_modules/@fullcalendar/common/main.css", "node_modules/@fullcalendar/daygrid/main.css", "node_modules/@fullcalendar/timegrid/main.css", "src/app/modules/gantt/src/gantt.scss"], "scripts": ["node_modules/prismjs/prism.js", "node_modules/prismjs/components/prism-typescript.js", "node_modules/quill/dist/quill.js"], "allowedCommonJsDependencies": ["@fullcalendar/daygrid", "@fullcalendar/timegrid", "@fullcalendar/interaction", "chart.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "morpheus:build"}, "configurations": {"production": {"browserTarget": "morpheus:build:production"}, "development": {"browserTarget": "morpheus:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "morpheus:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "styles": ["src/styles.scss"], "scripts": [], "assets": ["src/assets", "src/favicon.ico", "src/upload.php"]}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "defaultProject": "morpheus", "cli": {"schematicCollections": ["@angular-eslint/schematics"], "analytics": "09d6fd2b-0284-483e-b187-820c5fc2b5e7"}}