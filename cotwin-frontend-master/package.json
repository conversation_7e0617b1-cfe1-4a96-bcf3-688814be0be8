{"name": "co-twin", "version": "1.0.0", "license": "PrimeNG Commercial", "scripts": {"ng": "ng", "start": "ng serve --disable-host-check --proxy-config proxy.conf.json", "start-local": "ng serve  --disable-host-check --proxy-config proxy-local.conf.json", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "~14.2.12", "@angular/cdk": "~14.2.7", "@angular/common": "~14.2.12", "@angular/compiler": "~14.2.12", "@angular/core": "~14.2.12", "@angular/forms": "~14.2.12", "@angular/platform-browser": "~14.2.12", "@angular/platform-browser-dynamic": "~14.2.12", "@angular/router": "~14.2.12", "@fullcalendar/angular": "^5.8.0", "@fullcalendar/core": "^5.8.0", "@fullcalendar/daygrid": "^5.8.0", "@fullcalendar/interaction": "^5.8.0", "@fullcalendar/timegrid": "^5.8.0", "@ngrx/store": "^14.3.2", "@ngx-translate/core": "14.0.0", "@ngx-translate/http-loader": "7.0.0", "@google/model-viewer": "^1.9.0", "chart.js": "^4.1.1", "primeflex": "3.3.0", "primeicons": "6.0.1", "primeng": "14.2.3", "prismjs": "^1.29.0", "quill": "^1.3.7", "rxjs": "~6.6.0", "three": "^0.154.0", "tslib": "^2.0.0", "web-animations-js": "^2.3.2", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^14.2.10", "@angular-eslint/builder": "14.4.0", "@angular-eslint/eslint-plugin": "14.4.0", "@angular-eslint/eslint-plugin-template": "14.4.0", "@angular-eslint/schematics": "14.4.0", "@angular-eslint/template-parser": "14.4.0", "@angular/cli": "~14.2.10", "@angular/compiler-cli": "~14.2.12", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.8", "@types/node": "^12.11.1", "@typescript-eslint/eslint-plugin": "^5.36.2", "@typescript-eslint/parser": "^5.36.2", "eslint": "^8.23.0", "jasmine-core": "~5.0.1", "jasmine-spec-reporter": "~5.0.0", "karma": "^6.4.1", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "protractor": "~7.0.0", "ts-node": "~8.3.0", "typescript": "~4.8.4"}}