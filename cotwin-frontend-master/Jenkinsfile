def stagesMap = [
    'Yarn Install',
    'Yarn Build',
]

pipeline {
    agent any

    stages {
        stage("Yarn Install") {
            steps {
                script {
                    failedStage = env.STAGE_NAME
                    stagesMap.each { value ->
                        publishChecks name: "${value}", status: 'QUEUED'
                    }
                }
                publishChecks name: 'Yarn Install', status: 'IN_PROGRESS'
                dir('') {
                    echo 'yarn install...'
                    nodejs('Node-16.16.0') {
                        sh 'yarn install'
                    }
                }
                publishChecks name: 'Yarn Install', title: 'Yarn Install', summary: 'yarn install',
                    text: 'running yarn install',
                    detailsURL: ''
                script { stagesMap.remove(0) }
            }
        }
        stage("Yarn Build") {
            steps {
                script { failedStage = env.STAGE_NAME }
                publishChecks name: 'Yarn Build', status: 'IN_PROGRESS'
                dir('') {
                    echo 'yarn build...'
                    nodejs('Node-16.16.0') {
                        sh 'yarn build'
                    }
                }
                publishChecks name: 'Yarn Build'
                script { stagesMap.remove(0) }
            }
        }

        stage("Deploy") {
            when {
                branch "master"
            }
            steps {
                script { failedStage = env.STAGE_NAME }
                publishChecks name: 'Deploy', status: 'IN_PROGRESS'
                sshagent(credentials: ['cotwinfrontendprivkey']) {
                    echo 'Connected to server'
                    sh "ssh -o StrictHostKeyChecking=no -l cotwinfrontendgitlabuser fileserver.dakikyazilim.com 'mkdir /var/www/cotwin/cotwin-frontend/dist/_tmp'"
                    sh "scp -r dist/* <EMAIL>:/var/www/cotwin/cotwin-frontend/dist/_tmp"
                    sh "ssh -o StrictHostKeyChecking=no -l cotwinfrontendgitlabuser fileserver.dakikyazilim.com 'mv /var/www/cotwin/cotwin-frontend/dist/live /var/www/cotwin/cotwin-frontend/dist/_old && mv /var/www/cotwin/cotwin-frontend/dist/_tmp /var/www/cotwin/cotwin-frontend/dist/live && rm -rf /var/www/cotwin/cotwin-frontend/dist/_old'"
                }
                publishChecks name: 'Deploy'
            }
        }
    }
    post {
        failure {
            script {
                stagesMap.each { value ->
                    publishChecks name: "${value}", conclusion: 'CANCELED', status: 'COMPLETED'
                }
            }
            publishChecks name: "${failedStage}", conclusion: 'FAILURE',  status: 'COMPLETED'
        }
    }
}
