# Changelog

## 13.1.0
**Migration Guide**

- Upgrade to PrimeFlex v3.
- Upgrade to Angular 13 and PrimeNG 13.1.0

## 13.0.0
**Migration Guide**
- Update your project to Angular 13.
- Update theme files and layout files.

**Implemented New Features and Enhancements:**

- Upgrade to Angular and PrimeNG 13

## 12.2.0
**Migration Guide**

- Update theme files and layout files.

**Implemented New Features and Enhancements:**

- Styles of new PrimeNG components

## 12.0.0
**Migration Guide**
- Update your project to Angular 12.
- Update app.menuitem.component.ts
- Update theme files and layout files.

**Implemented New Features and Enhancements:**

- Upgrade to Angular and PrimeNG 12
- Styles of new PrimeNG components

## 11.0.0
**Migration Guide**
- Update your project to Angular 11.
- Update app.main.component.ts, app.main.component.html and app.component.ts.
- Update app.topbar.component.ts and app.topbar.component.html.
- Update theme files and layout files.

**Implemented New Features and Enhancements:**

- Upgrade to Angular and PrimeNG 11
- Styles of new PrimeNG components

## 10.0.0
**Migration Guide**
- Update your project to Angular 10.
- Update theme files and layout files.

**Implemented New Features and Enhancements:**

- Upgrade to Angular and PrimeNG 10
- Migrate to PrimeOne Design Architecture

#### 8.0.0 to 9.0.0
Angular 9 and PrimeNG 9
- Update dependencies with <a href="https://angular.io/cli/update">ng update</a>.
- Update theme.css and layout.css files.
- Update app.sidebartabcontent.component.ts.
- Update app.menu.component.ts while retaining your MenuModel.
- Include app.menuitem.component.ts under app folder and define it app.module.ts with declarations property.
- Include app.menu.service.ts under app folder and define it app.module.ts with providers property.

#### 7.0.0 to 8.0.0
- Update your project to Angular 8 with ng update. View the official update guide -<a href="https://update.angular.io/">https://update.angular.io/</a>- for more information.
- Update app.component.ts.
- Update app.menu.component.ts.
- Update app.sidebartabcontent.component.ts.
- Update layout css files.
- Update theme css files.

#### 6.1.1 to 7.0.0
- Update layout css files.
- Update theme css files.

#### 6.1.0 to 6.1.1
- Update layout css files.
- Update theme css files.

#### 6.0.0 to 6.1.0
Adds support for new features in PrimeNG 6.1.x
- Update layout css files.
- Update theme css files.

#### 5.2.0 to 6.0.0
Brings support for Angular 6 and RxJS 6, adds theming for new components in PrimeNG such as the new TreeTable
and improves behaviors of the layout menus.
- Add PrimeIcons package.
- Update app.module.ts.
- Update app.component.ts.
- Update app.menu.component.ts.
- Update app.topbar.component.html.
- Update layout css files.
- Update theme css files.

#### 5.0.0 to 5.2.0
Adds support for PrimeNG 5.2.0 (e.g. TurboTable), replaces nanoscroller with PrimeNG ScrollPanel.
- Remove nanoscroller as it is replaced by ScrollPanel component of PrimeNG.
- Update app.sidebartabcontent.component.ts.
- Update layout css files.
- Update theme css files.

#### 1.2.0 to 5.0.0
- Update theme css files.
- Includes version updates to PrimeNG 5 and Angular 5.

#### 1.1.0 to 1.2.0
- Update theme css files.

#### 1.0.1 to 1.1.0
- Update layout css files.
- Update theme css files.
- Update AppSubmenu component in app.menu.component.ts.

#### 1.0.0 to 1.0.1
- Update theme-*.css files.
